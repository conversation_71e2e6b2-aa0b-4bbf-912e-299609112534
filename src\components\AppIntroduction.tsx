import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowRight, Sparkles, Users, BarChart3, Heart, Shield, Zap } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { hapticFeedback } from '@/utils/haptics';
import CustomSplashScreen from './CustomSplashScreen';

interface IntroPage {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: string;
}

const introPages: IntroPage[] = [
  {
    id: 1,
    title: 'Welcome to ChatBuster',
    description: '',
    icon: <Sparkles className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 2,
    title: 'Transform Your Conversations',
    description: 'Turn your everyday chats into amazing insights. Our algorithm which supported with smart AIs looks at how your group talks and shows you the special things that make your connection stronger.',
    icon: <Heart className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 3,
    title: 'Squad Vibes',
    description: 'See everyone\'s personality and archetypes from 26 different traits, how they chat, and what role they play. Click Squad Vibes in the bottom navigation bar to explore more.',
    icon: <Users className="w-16 h-16 text-white" />,
    gradient: 'from-blue-600 to-indigo-600'
  },
  {
    id: 4,
    title: 'Connections',
    description: 'Explore how your group members connect with each other. Find out who gets along best, discover friendship patterns, and see the bonds that bring everyone together. Click Connections in the bottom navigation bar to explore more.',
    icon: <Zap className="w-16 h-16 text-white" />,
    gradient: 'from-indigo-600 to-purple-600'
  },
  {
    id: 5,
    title: 'Your Privacy Matters',
    description: 'We keep your messages completely safe and private. Everything is stored only on your phone and follow the international and EU regulations. We never read your personal conversations or share them with anyone.',
    icon: <Shield className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-pink-600'
  },
  {
    id: 6,
    title: "Ready to Get Started?",
    description: 'Let\'s take a look at your conversations and discover the amazing things happening in your chats. See what makes your friendships special!',
    icon: <BarChart3 className="w-16 h-16 text-white" />,
    gradient: 'from-pink-600 to-purple-600'
  }
];

const AppIntroduction: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(0);
  const [showSplash, setShowSplash] = useState(true);
  const [logoAnimated, setLogoAnimated] = useState(false);

  // Handle splash screen and logo animation
  useEffect(() => {
    const splashTimer = setTimeout(() => {
      setShowSplash(false);
      // Start logo animation after splash
      setTimeout(() => {
        setLogoAnimated(true);
      }, 500);
    }, 3000);

    return () => clearTimeout(splashTimer);
  }, []);

  const nextPage = () => {
    if (currentPage < introPages.length - 1) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFinish = () => {
    hapticFeedback.buttonPress();
    // Navigate to tutorial/analyze chat page
    navigate('/', { replace: true });
  };

  // Use CustomSplashScreen
  if (showSplash) {
    return <CustomSplashScreen />;
  }

  const currentPageData = introPages[currentPage];
  const isLastPage = currentPage === introPages.length - 1;

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ backgroundColor: '#f0bbcc' }}>

      {/* Animated Logo Section - Only show on welcome page */}
      {currentPage === 0 && (
        <motion.div
          initial={{
            position: "absolute",
            top: "50%",
            left: "50%",
            x: "-50%",
            y: "-50%"
          }}
          animate={{
            position: logoAnimated ? "absolute" : "absolute",
            top: logoAnimated ? "80px" : "50%",
            left: "50%",
            x: "-50%",
            y: logoAnimated ? "0%" : "-50%"
          }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
          className="z-20"
          style={{ pointerEvents: logoAnimated ? 'none' : 'auto' }}
        >
          <motion.div
            initial={{ scale: 1 }}
            animate={{ scale: logoAnimated ? 1.2 : 1 }}
            transition={{ duration: 1.2, ease: "easeInOut" }}
            className="relative flex flex-col items-center"
          >
            <img
              src="/icons/header_logo.webp"
              alt="Chatbuster Logo"
              className="w-48 h-24 object-contain mb-4"
            />
            
            {/* Invisible placeholder for loading circle space */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0 }}
              className="relative w-16 h-16"
            />
          </motion.div>
        </motion.div>
      )}

      {/* Main Content */}
      <AnimatePresence mode="wait">
        {logoAnimated && (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className={`flex flex-col min-h-screen px-6 ${currentPage === 0 ? 'pt-40' : 'pt-20'}`}
          >
            {/* Page Content - Flexible space */}
            <div className="flex-1 flex items-center justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.4 }}
                  className="text-center max-w-md mx-auto"
                >
                  {currentPage === 0 ? (
                    /* Welcome page - title only */
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.6 }}
                      className="text-center"
                    >
                      <h2 className="text-5xl font-bold text-white">
                        Welcome to ChatBuster
                      </h2>
                    </motion.div>
                  ) : (
                    /* Info pages */
                    <>
                      {/* Title */}
                      <h3 className={`text-3xl sm:text-4xl font-bold text-white ${currentPage === 2 ? 'mb-4' : 'mb-6'}`}>
                        {currentPageData.title}
                      </h3>

                      {/* Description with bigger font size */}
                      <p className={`text-white/90 text-lg sm:text-xl leading-relaxed font-medium ${currentPage === 2 ? 'px-2 sm:px-4 mb-6' : ''}`}>
                        {currentPageData.description}
                      </p>

                      {/* Squad Vibes Image - Only show on page 3, under description */}
                      {currentPage === 2 && (
                        <div className="mb-8 flex justify-center px-2 sm:px-4">
                          <div className="relative w-full max-w-md sm:max-w-lg md:max-w-xl h-32 sm:h-36 md:h-40 rounded-2xl overflow-hidden">
                            <img
                              src="/intro_image/image.png"
                              alt="Squad Vibes Navigation"
                              className="w-full h-full object-contain scale-120 hover:scale-120 transition-transform duration-300"
                              onError={(e) => {
                                // Fallback if image doesn't load
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Fixed Bottom Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: currentPage === 0 ? 1.2 : 0.8, duration: 0.6 }}
              className="pb-8 w-full max-w-md mx-auto"
            >
              <Button
                onClick={isLastPage ? handleFinish : nextPage}
                className="w-full h-12 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 text-lg font-semibold transition-all duration-300"
              >
                {isLastPage ? (
                  <>
                    Let's Analyze 
                    <Sparkles className="w-5 h-5 ml-2" />
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AppIntroduction;
